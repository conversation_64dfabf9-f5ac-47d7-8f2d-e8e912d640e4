import { Button, Drawer, Input, Label, Select, toast } from "@medusajs/ui"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useUpdateCompany } from "../../../../../hooks/api/companies"
import { useRegions } from "../../../../../hooks/api/regions"
import { Company, AdminUpdateCompany } from "../../../../../types"

interface CompanyUpdateDrawerProps {
  company: Company
  open: boolean
  setOpen: (open: boolean) => void
}

export const CompanyUpdateDrawer = ({ company, open, setOpen }: CompanyUpdateDrawerProps) => {
  const { t } = useTranslation()
  const [formData, setFormData] = useState({
    name: company.name || "",
    email: company.email || "",
    phone: company.phone || "",
    address: company.address || "",
    city: company.city || "",
    state: company.state || "",
    zip: company.zip || "",
    country: company.country,
    currency_code: company.currency_code || "",
    logo_url: company.logo_url || "",
  })

  const { mutateAsync, isPending, error } = useUpdateCompany(company.id, {
    onSuccess: () => {
      toast.success(t("companies.toasts.updateSuccess"))
      setOpen(false)
    },
    onError: () => {
      toast.error(t("companies.toasts.updateError"))
    },
  })

  const { regions, isPending: regionsLoading } = useRegions({
    fields: "*countries",
  })

  const currencyCodes = regions?.map((region) => region.currency_code)
  const countries = regions?.flatMap((region) => region.countries)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value })
  }

  const handleCountryChange = (value: string) => {
    setFormData({ ...formData, country: value })
  }

  const handleCurrencyChange = (value: string) => {
    setFormData({ ...formData, currency_code: value })
  }

  const handleSubmit = async () => {
    await mutateAsync(formData)
  }

  return (
    <Drawer open={open} onOpenChange={setOpen}>
      <Drawer.Content className="z-50">
        <Drawer.Header>
          <Drawer.Title>{t("companies.actions.edit")}</Drawer.Title>
        </Drawer.Header>

        <form>
          <Drawer.Body className="p-4">
            <div className="flex flex-col gap-2">
              <Label size="xsmall">{t("companies.fields.name")}</Label>
              <Input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Medusa"
              />

              <Label size="xsmall">{t("companies.fields.phone")}</Label>
              <Input
                type="text"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="1234567890"
              />

              <Label size="xsmall">{t("companies.fields.email")}</Label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
              />

              <Label size="xsmall">{t("companies.fields.address")}</Label>
              <Input
                type="text"
                name="address"
                value={formData.address || ""}
                onChange={handleChange}
                placeholder="1234 Main St"
              />

              <Label size="xsmall">{t("companies.fields.city")}</Label>
              <Input
                type="text"
                name="city"
                value={formData.city || ""}
                onChange={handleChange}
                placeholder="New York"
              />

              <Label size="xsmall">{t("companies.fields.state")}</Label>
              <Input
                type="text"
                name="state"
                value={formData.state || ""}
                onChange={handleChange}
                placeholder="NY"
              />

              <Label size="xsmall">{t("companies.fields.zip")}</Label>
              <Input
                type="text"
                name="zip"
                value={formData.zip || ""}
                onChange={handleChange}
                placeholder="10001"
              />

              <div className="flex gap-4 w-full">
                <div className="flex flex-col gap-2 w-1/2">
                  <Label size="xsmall">{t("companies.fields.country")}</Label>
                  <Select
                    name="country"
                    value={formData.country}
                    onValueChange={handleCountryChange}
                    disabled={regionsLoading}
                  >
                    <Select.Trigger disabled={regionsLoading}>
                      <Select.Value placeholder={t("companies.placeholders.selectCountry")} />
                    </Select.Trigger>
                    <Select.Content className="z-50">
                      {countries?.map((country) => (
                        <Select.Item
                          key={country?.iso_2 || ""}
                          value={country?.iso_2 || ""}
                        >
                          {country?.name}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </div>
                <div className="flex flex-col gap-2 w-1/2">
                  <Label size="xsmall">{t("companies.fields.currency")}</Label>
                  <Select
                    name="currency_code"
                    value={formData.currency_code || undefined}
                    onValueChange={handleCurrencyChange}
                    defaultValue={currencyCodes?.[0]}
                    disabled={regionsLoading}
                  >
                    <Select.Trigger disabled={regionsLoading}>
                      <Select.Value placeholder={t("companies.placeholders.selectCurrency")} />
                    </Select.Trigger>
                    <Select.Content className="z-50">
                      {currencyCodes?.map((currencyCode) => (
                        <Select.Item key={currencyCode} value={currencyCode}>
                          {currencyCode?.toUpperCase()}
                        </Select.Item>
                      ))}
                    </Select.Content>
                  </Select>
                </div>
              </div>

              <Label size="xsmall">{t("companies.fields.logoUrl")}</Label>
              <Input
                type="text"
                name="logo_url"
                value={formData.logo_url || ""}
                onChange={handleChange}
                placeholder="https://example.com/logo.png"
              />
            </div>
          </Drawer.Body>

          <Drawer.Footer>
            <Drawer.Close asChild>
              <Button variant="secondary">{t("actions.cancel")}</Button>
            </Drawer.Close>
            <Button
              isLoading={isPending}
              onClick={handleSubmit}
            >
              {t("actions.save")}
            </Button>
            {error && (
              <Text className="txt-compact-small text-ui-fg-warning">
                {t("common.error")}: {error?.message}
              </Text>
            )}
          </Drawer.Footer>
        </form>
      </Drawer.Content>
    </Drawer>
  )
}
